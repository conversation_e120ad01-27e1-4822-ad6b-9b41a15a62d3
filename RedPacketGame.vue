<template>
  <div class="game-container">
    <div class="game-header">
      <h1>🧧 接红包小游戏 🧧</h1>
      <div class="score-board">
        <span>得分: <span>{{ score }}</span></span>
        <span>生命: <span>{{ lives }}</span></span>
      </div>
    </div>
    
    <div class="game-area" ref="gameArea" @touchstart="handleTouch" @touchmove="handleTouch">
      <!-- 精灵角色 -->
      <div class="player" ref="player" :style="{ left: playerPosition + 'px' }">🧙‍♂️</div>
      
      <!-- 红包 -->
      <div 
        v-for="packet in redPackets" 
        :key="packet.id"
        class="red-packet"
        :style="{ 
          left: packet.x + 'px', 
          top: packet.y + 'px',
          animationDuration: packet.fallDuration + 'ms'
        }"
      >
        🧧
      </div>
      
      <!-- 游戏说明 -->
      <div class="instructions" v-if="!gameStarted && !gameEnded">
        <p>使用 ← → 方向键或 A D 键控制精灵移动</p>
        <p>接住红包得分，错过红包扣生命</p>
        <button @click="startGame">开始游戏</button>
      </div>
      
      <!-- 游戏结束界面 -->
      <div class="game-over" v-if="gameEnded">
        <h2>游戏结束</h2>
        <p>最终得分: <span>{{ score }}</span></p>
        <button @click="restartGame">重新开始</button>
      </div>
      
      <!-- 收集效果 -->
      <div 
        v-for="effect in collectEffects" 
        :key="effect.id"
        class="collect-effect"
        :style="{ left: effect.x + 'px', top: effect.y + 'px' }"
      >
        +10
      </div>
    </div>
    
    <div class="controls">
      <p>控制说明：使用方向键 ← → 或 A D 键移动精灵</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RedPacketGame',
  data() {
    return {
      gameWidth: 0,
      gameHeight: 0,
      playerWidth: 60,
      playerHeight: 60,
      score: 0,
      lives: 3,
      gameRunning: false,
      gameStarted: false,
      gameEnded: false,
      playerPosition: 0,
      redPackets: [],
      collectEffects: [],
      gameSpeed: 1,
      packetIdCounter: 0,
      effectIdCounter: 0,
      keys: {
        left: false,
        right: false
      },
      gameLoopId: null,
      spawnTimeoutId: null
    }
  },
  mounted() {
    this.init()
  },
  beforeUnmount() {
    this.cleanup()
  },
  methods: {
    init() {
      this.gameWidth = this.$refs.gameArea.offsetWidth
      this.gameHeight = this.$refs.gameArea.offsetHeight
      this.playerPosition = this.gameWidth / 2 - this.playerWidth / 2
      
      // 键盘事件监听
      document.addEventListener('keydown', this.handleKeyDown)
      document.addEventListener('keyup', this.handleKeyUp)
    },
    
    cleanup() {
      document.removeEventListener('keydown', this.handleKeyDown)
      document.removeEventListener('keyup', this.handleKeyUp)
      if (this.gameLoopId) {
        cancelAnimationFrame(this.gameLoopId)
      }
      if (this.spawnTimeoutId) {
        clearTimeout(this.spawnTimeoutId)
      }
    },
    
    handleKeyDown(e) {
      switch(e.code) {
        case 'ArrowLeft':
        case 'KeyA':
          this.keys.left = true
          e.preventDefault()
          break
        case 'ArrowRight':
        case 'KeyD':
          this.keys.right = true
          e.preventDefault()
          break
      }
    },
    
    handleKeyUp(e) {
      switch(e.code) {
        case 'ArrowLeft':
        case 'KeyA':
          this.keys.left = false
          break
        case 'ArrowRight':
        case 'KeyD':
          this.keys.right = false
          break
      }
    },
    
    handleTouch(e) {
      e.preventDefault()
      const touch = e.touches[0]
      const rect = this.$refs.gameArea.getBoundingClientRect()
      const touchX = touch.clientX - rect.left
      
      if (touchX < this.gameWidth / 2) {
        this.keys.left = true
        this.keys.right = false
      } else {
        this.keys.right = true
        this.keys.left = false
      }
      
      if (e.type === 'touchend') {
        this.keys.left = false
        this.keys.right = false
      }
    },
    
    startGame() {
      this.gameRunning = true
      this.gameStarted = true
      this.gameEnded = false
      this.gameLoop()
      this.spawnRedPackets()
    },
    
    restartGame() {
      this.score = 0
      this.lives = 3
      this.gameSpeed = 1
      this.playerPosition = this.gameWidth / 2 - this.playerWidth / 2
      this.redPackets = []
      this.collectEffects = []
      this.gameEnded = false
      this.startGame()
    },
    
    gameLoop() {
      if (!this.gameRunning) return
      
      this.updatePlayer()
      this.updateRedPackets()
      this.checkCollisions()
      
      this.gameLoopId = requestAnimationFrame(this.gameLoop)
    },
    
    updatePlayer() {
      const moveSpeed = 5
      
      if (this.keys.left && this.playerPosition > 0) {
        this.playerPosition -= moveSpeed
      }
      if (this.keys.right && this.playerPosition < this.gameWidth - this.playerWidth) {
        this.playerPosition += moveSpeed
      }
    },
    
    spawnRedPackets() {
      if (!this.gameRunning) return
      
      this.createRedPacket()
      
      // 随机间隔生成红包，游戏速度越快间隔越短
      const spawnDelay = Math.max(800 - this.gameSpeed * 50, 300)
      this.spawnTimeoutId = setTimeout(() => this.spawnRedPackets(), spawnDelay + Math.random() * 500)
    },
    
    createRedPacket() {
      const x = Math.random() * (this.gameWidth - 40)
      const fallDuration = Math.max(3000 - this.gameSpeed * 100, 1500)
      const speed = this.gameHeight / (fallDuration / 16.67)
      
      const packet = {
        id: this.packetIdCounter++,
        x: x,
        y: -50,
        speed: speed,
        fallDuration: fallDuration
      }
      
      this.redPackets.push(packet)
      
      // 红包掉落完成后移除
      setTimeout(() => {
        const index = this.redPackets.findIndex(p => p.id === packet.id)
        if (index !== -1) {
          this.redPackets.splice(index, 1)
          this.loseLife()
        }
      }, fallDuration)
    },
    
    updateRedPackets() {
      this.redPackets.forEach(packet => {
        packet.y += packet.speed
      })
    },
    
    checkCollisions() {
      this.redPackets.forEach((packet, index) => {
        const packetRect = {
          x: packet.x,
          y: packet.y,
          width: 40,
          height: 40
        }
        
        const playerRect = {
          x: this.playerPosition,
          y: this.gameHeight - 70,
          width: this.playerWidth,
          height: this.playerHeight
        }
        
        if (this.isColliding(packetRect, playerRect)) {
          this.collectRedPacket(packet, index)
        }
      })
    },
    
    isColliding(rect1, rect2) {
      return rect1.x < rect2.x + rect2.width &&
             rect1.x + rect1.width > rect2.x &&
             rect1.y < rect2.y + rect2.height &&
             rect1.y + rect1.height > rect2.y
    },
    
    collectRedPacket(packet, index) {
      this.redPackets.splice(index, 1)
      this.score += 10
      
      // 每100分增加游戏速度
      if (this.score % 100 === 0) {
        this.gameSpeed++
      }
      
      // 添加收集效果
      this.showCollectEffect(packet.x, packet.y)
    },
    
    showCollectEffect(x, y) {
      const effect = {
        id: this.effectIdCounter++,
        x: x,
        y: y
      }
      
      this.collectEffects.push(effect)
      
      setTimeout(() => {
        const index = this.collectEffects.findIndex(e => e.id === effect.id)
        if (index !== -1) {
          this.collectEffects.splice(index, 1)
        }
      }, 1000)
    },
    
    loseLife() {
      this.lives--
      
      if (this.lives <= 0) {
        this.endGame()
      }
    },
    
    endGame() {
      this.gameRunning = false
      this.gameEnded = true
      this.redPackets = []
      this.collectEffects = []
      
      if (this.spawnTimeoutId) {
        clearTimeout(this.spawnTimeoutId)
      }
    }
  }
}
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.game-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  width: 100%;
  font-family: 'Arial', sans-serif;
}

.game-header {
  text-align: center;
  margin-bottom: 20px;
}

.game-header h1 {
  color: #d63384;
  font-size: 2.5em;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.score-board {
  display: flex;
  justify-content: space-around;
  font-size: 1.2em;
  font-weight: bold;
  color: #495057;
}

.game-area {
  position: relative;
  width: 100%;
  height: 500px;
  background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 100%);
  border: 3px solid #ffc107;
  border-radius: 15px;
  overflow: hidden;
  margin-bottom: 20px;
}

.player {
  position: absolute;
  bottom: 10px;
  font-size: 3em;
  z-index: 10;
  transition: left 0.1s ease;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

.red-packet {
  position: absolute;
  font-size: 2em;
  z-index: 5;
  animation: fall linear;
  filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.2));
}

@keyframes fall {
  from {
    top: -50px;
  }
  to {
    top: 100%;
  }
}

.instructions, .game-over {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.95);
  padding: 30px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  z-index: 20;
}

.instructions p, .game-over p {
  margin-bottom: 15px;
  color: #495057;
  font-size: 1.1em;
}

.instructions h2, .game-over h2 {
  color: #d63384;
  margin-bottom: 20px;
  font-size: 2em;
}

button {
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  color: white;
  border: none;
  padding: 12px 25px;
  font-size: 1.1em;
  border-radius: 25px;
  cursor: pointer;
  transition: transform 0.2s ease;
  font-weight: bold;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

button:active {
  transform: translateY(0);
}

.controls {
  text-align: center;
  color: #495057;
  font-size: 0.9em;
  background: rgba(255, 255, 255, 0.7);
  padding: 10px;
  border-radius: 10px;
}

.collect-effect {
  position: absolute;
  color: #ff6b6b;
  font-weight: bold;
  font-size: 1.5em;
  z-index: 15;
  pointer-events: none;
  animation: fadeUp 1s ease-out forwards;
}

@keyframes fadeUp {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-30px);
  }
}

/* 响应式设计 */
@media (max-width: 600px) {
  .game-container {
    margin: 10px;
    padding: 15px;
  }

  .game-header h1 {
    font-size: 2em;
  }

  .game-area {
    height: 400px;
  }

  .player {
    font-size: 2.5em;
  }

  .red-packet {
    font-size: 1.5em;
  }
}
</style>
